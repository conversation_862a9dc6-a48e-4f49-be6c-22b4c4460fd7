
import React from 'react';

interface NewsSummaryWidgetProps {
  summary: string;
  postId: string;
  isExpanded: boolean;
  onToggle: () => void;
}

const NewsSummaryWidget: React.FC<NewsSummaryWidgetProps> = ({
  summary,
  postId,
  isExpanded,
  onToggle
}) => {


  return (
    <div className="space-y-4">
      {/* Modern Summary Card */}
      <div className="relative">
        {/* Main content area with modern gradient design */}
        <div className="bg-gradient-to-br from-white via-blue-50/20 to-white rounded-xl border border-blue-100 shadow-sm hover:shadow-lg hover:border-blue-200 transition-all duration-300">
          <div className="p-6">
            {/* Summary points */}
            <div className="space-y-5">
              {summary.split('\n').map((line, index) => {
                const trimmedLine = line.trim();
                if (!trimmedLine) return null;

                return (
                  <div
                    key={index}
                    className="group slide-up"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="flex items-start gap-4 p-3 rounded-lg hover:bg-blue-50/40 transition-colors duration-200">
                      {/* Modern bullet point with gradient */}
                      <div className="flex-shrink-0 mt-2">
                        <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full group-hover:scale-125 group-hover:shadow-sm transition-all duration-200"></div>
                      </div>
                      {/* Content */}
                      <p className="text-slate-700 leading-relaxed text-sm sm:text-base font-medium group-hover:text-slate-900 transition-colors duration-200">
                        {trimmedLine.replace(/^•\s*/, '')}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Modern accent line with gradient */}
          <div className="h-2 bg-gradient-to-r from-blue-200 via-blue-500 to-blue-200 rounded-b-xl"></div>
        </div>

        {/* Modern floating elements */}
        <div className="absolute -top-2 -right-2 w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full animate-pulse shadow-sm"></div>
        <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-gradient-to-r from-blue-300 to-blue-200 rounded-full animate-pulse shadow-sm" style={{ animationDelay: '1s' }}></div>
      </div>
    </div>
  );
};

export default NewsSummaryWidget;
